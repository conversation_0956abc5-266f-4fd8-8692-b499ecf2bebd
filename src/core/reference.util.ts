import * as portal from '@EmmySoft-GmbH/emmysoft-client';
import { Checksum } from '@core/checksum-service.interface';

export enum RefEntity {
  PATH = 'path',
  STEP = 'step',
  STEP_GROUP_SELECTOR = 'stepGroupSelector',
  COMPANY = 'company',
  COMPANY_PERSON = 'companyPerson',
  JOB = 'job',
  JOB_PERSON = 'jobPerson',
  PERSON = 'person',
  JOB_APPLICATION = 'jobApplication',
  JOB_APPLICATION_PROGRESS = 'jobApplicationProgress',
  CANDIDATE = 'candidate',
  EDUCATION = 'education',
  REJECTION = 'rejection',
  REJECTION_REASON = 'rejectionReason',
  DOCUMENT = 'document',
  CANDIDATE_DOCUMENT = 'candidateDocument',
  JOB_APPLICATION_DOCUMENT = 'jobApplicationDocument',
  LANGUAGE_SKILL = 'languageSkill',
  POSITION = 'position',
}

export class ReferenceUtil {
  /**
   * Key with meffert source i.e. 'source:mainId'
   * @private
   */
  public static readonly COMPOSITE_ID = 'meffert_composite_id';

  /**
   * Key without meffert source i.e. 'ID'
   * @private
   */
  public static readonly ID = 'meffert_id';

  public static readonly ID2 = 'meffert_id_2';

  public static readonly CHECKSUM = `meffert_checksum`;

  public static readonly CHECKSUM_REJECT = `meffert_reject_checksum`;

  /**
   * Create reference with composite ID (COMPOSITE_ID).
   * Used for creating entities with composite ID (i.e. source and id).
   * @param refEntity
   * @param ids
   */
  static withCompositeId(refEntity: RefEntity, ...ids: string[]): portal.Reference {
    return portal.createReference(ReferenceUtil.COMPOSITE_ID, refEntity, ReferenceUtil.buildCompositeId(ids));
  }

  /**
   * Create reference with id only (ID).
   * Used for search functionality by ID only.
   * @param refEntity
   * @param id
   */
  static withId(refEntity: RefEntity, id: string): portal.Reference {
    return portal.createReference(ReferenceUtil.ID, refEntity, id);
  }

  static addId(ref: portal.Reference, refEntity: RefEntity, id: string): void {
    ref.addReference(ReferenceUtil.ID, portal.constructStringReferenceValue(refEntity, id));
  }

  static addCustomId(customId: string, ref: portal.Reference, refEntity: RefEntity, id: string): void {
    ref.addReference(customId, portal.constructStringReferenceValue(refEntity, id));
  }

  static addChecksum(ref: portal.Reference, checksum: string): void {
    ref.addReference(ReferenceUtil.CHECKSUM, checksum);
  }

  static addChecksumReject(ref: portal.Reference, checksum: string): void {
    ref.addReference(ReferenceUtil.CHECKSUM_REJECT, checksum);
  }

  static getChecksum(reference: Record<string, unknown>, checksumLocation?: string): Checksum | undefined {
    if (!reference) {
      return undefined;
    }

    const compositeID = reference[ReferenceUtil.COMPOSITE_ID] as string;
    const checksum = checksumLocation
      ? (reference[checksumLocation] as string)
      : (reference[ReferenceUtil.CHECKSUM] as string);

    if (!compositeID) {
      return undefined;
    }

    // compositeId value is i.e: job#source-value:id-value
    const sourceAndIdRaw = compositeID.split('#')[1];
    const sourceAndId = sourceAndIdRaw.split(':');
    return new Checksum({
      source: sourceAndId[0],
      id: sourceAndId[1],
      id2: sourceAndId[2] ? sourceAndId[2] : undefined,
      checksum: checksum,
    });
  }

  /**
   * Gets checksum information from a job reference and includes EmmyScoreID
   * @param reference The job reference object
   * @param checksumLocation Optional custom location for the checksum value
   * @returns Checksum object with EmmyScoreID or undefined if reference is invalid
   */
  static getJobChecksum(reference: Record<string, unknown>, checksumLocation?: string): Checksum | undefined {
    if (!reference) {
      return undefined;
    }

    const compositeID = reference[ReferenceUtil.COMPOSITE_ID] as string;
    const checksum = checksumLocation
      ? (reference[checksumLocation] as string | undefined)
      : (reference[ReferenceUtil.CHECKSUM] as string | undefined);
    const emmyScoreID = reference['moonwalk'] as string | undefined;

    if (!compositeID) {
      return undefined;
    }

    // compositeId value is i.e: job#source-value:id-value
    const sourceAndIdRaw = compositeID.split('#')[1];
    const sourceAndId = sourceAndIdRaw.split(':');
    return new Checksum({
      source: sourceAndId[0],
      id: sourceAndId[1],
      id2: sourceAndId[2] ? sourceAndId[2] : undefined,
      checksum: checksum || '',
      EmmyScoreID: emmyScoreID,
    });
  }

  private static buildCompositeId(ids: string[]): string {
    return ids.join(':');
  }
}
