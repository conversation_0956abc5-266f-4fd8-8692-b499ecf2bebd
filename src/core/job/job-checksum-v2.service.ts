import { Inject, Injectable } from '@nestjs/common';
import { Checksum, ChecksumService } from '@core/checksum-service.interface';
import { RefEntity, ReferenceUtil } from '@core/reference.util';
import { PAGINATION_DEFAULT_PAGE_SIZE, TENANT_SERVICE } from '@core/constants';
import { TenantService } from '@core/tenant/tenant-service.interface';
import { PORTAL_V4_SERVICE, PortalV4Service } from '@core/portal-v4.service.interface';
import { PaginatedResponse } from '@core/portal-v4.types';

@Injectable()
export class JobChecksumServiceV2Impl implements ChecksumService {
  constructor(
    @Inject(PORTAL_V4_SERVICE) private readonly portalService: PortalV4Service,
    @Inject(TENANT_SERVICE) private readonly tenantService: TenantService,
  ) {}

  async getChecksum(source: string, id: string): Promise<Checksum | undefined> {
    const tenantConfig = this.tenantService.getDefaultTenantConfig();
    const tenantId = tenantConfig.id;

    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, source, id);

    const job = await this.portalService.getProjectByReferenceKey({ tenantId }, ref.key, ref.value);
    if (!job) {
      return undefined;
    }
    return ReferenceUtil.getJobChecksum(job.reference);
  }
  async getAllChecksums(page = 1, pageSize = PAGINATION_DEFAULT_PAGE_SIZE): Promise<PaginatedResponse<Checksum>> {
    const tenantConfig = this.tenantService.getDefaultTenantConfig();
    const tenantId = tenantConfig.id;

    const jobs = await this.portalService.getProjects({ tenantId }, page, pageSize);
    const checksums = jobs.data.map(j => ReferenceUtil.getChecksum(j.reference)).filter(c => !!c);
    return {
      data: checksums,
      pagination: jobs.pagination,
    };
  }
}
