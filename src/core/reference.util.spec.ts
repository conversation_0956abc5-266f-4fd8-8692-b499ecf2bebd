import { RefEntity, ReferenceUtil } from '@core/reference.util';

describe('reference.util', () => {
  it('should create reference job reference', () => {
    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, 'source-test', 'id-test');
    ref.addReference(ReferenceUtil.CHECKSUM, 'checksum-value');

    expect(ref).toBeDefined();
    expect(ref.key).toBe(ReferenceUtil.COMPOSITE_ID);
    expect(ref.value).toBe(`job#source-test:id-test`);
    expect(ref.references[ReferenceUtil.CHECKSUM]).toBe('checksum-value');
  });
});

describe('reference.util - checksum', () => {
  it('should return undefined for checksum if references not defined', () => {
    const checksum = ReferenceUtil.getChecksum(undefined);
    expect(checksum).toBeUndefined();
  });

  it('should not return undefined for checksum if checksum is missing', () => {
    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, 'source-test', 'id-test');
    const checksum = ReferenceUtil.getChecksum(ref.references);
    expect(checksum).toBeDefined();
  });

  it('should create checksum from reference', () => {
    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, 'source-value', 'id-value');
    ref.addReference(ReferenceUtil.CHECKSUM, 'checksum-value');

    const checksum = ReferenceUtil.getChecksum(ref.references);

    expect(checksum).toBeDefined();
    expect(checksum.source).toBe('source-value');
    expect(checksum.id).toBe('id-value');
    expect(checksum.checksum).toBe('checksum-value');
  });
});
