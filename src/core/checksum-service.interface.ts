import { ApiProperty } from '@nestjs/swagger';
import { PaginatedResponse } from './portal-v4.types';

export interface ChecksumService {
  getChecksum(source: string, id: string, id2?: string): Promise<Checksum>;

  getAllChecksums(page?: number, pageSize?: number): Promise<PaginatedResponse<Checksum> | Checksum[]>;
}

export class Checksum {
  @ApiProperty()
  source: string;
  @ApiProperty()
  id: string;
  @ApiProperty()
  id2?: string;
  @ApiProperty()
  checksum: string;
  @ApiProperty()
  shared?: number;
  @ApiProperty()
  sharedDate?: Date;
  @ApiProperty()
  EmmyScoreID?: string;

  constructor(options: {
    source: string;
    id: string;
    id2?: string;
    checksum: string;
    shared?: number;
    sharedDate?: Date;
    EmmyScoreID?: string;
  }) {
    this.source = options.source;
    this.id = options.id;
    this.id2 = options.id2;
    this.checksum = options.checksum;
    this.shared = options.shared;
    this.sharedDate = options.sharedDate;
    this.EmmyScoreID = options.EmmyScoreID;
  }
}
