import { RefEntity, ReferenceUtil } from '@core/reference.util';

describe('reference.util', () => {
  it('should create reference job reference', () => {
    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, 'source-test', 'id-test');
    ref.addReference(ReferenceUtil.CHECKSUM, 'checksum-value');

    expect(ref).toBeDefined();
    expect(ref.key).toBe(ReferenceUtil.COMPOSITE_ID);
    expect(ref.value).toBe(`job#source-test:id-test`);
    expect(ref.references[ReferenceUtil.CHECKSUM]).toBe('checksum-value');
  });
});

describe('reference.util - checksum', () => {
  it('should return undefined for checksum if references not defined', () => {
    const checksum = ReferenceUtil.getChecksum(undefined);
    expect(checksum).toBeUndefined();
  });

  it('should not return undefined for checksum if checksum is missing', () => {
    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, 'source-test', 'id-test');
    const checksum = ReferenceUtil.getChecksum(ref.references);
    expect(checksum).toBeDefined();
  });

  it('should create checksum from reference', () => {
    const ref = ReferenceUtil.withCompositeId(RefEntity.JOB, 'source-value', 'id-value');
    ref.addReference(ReferenceUtil.CHECKSUM, 'checksum-value');

    const checksum = ReferenceUtil.getChecksum(ref.references);

    expect(checksum).toBeDefined();
    expect(checksum.source).toBe('source-value');
    expect(checksum.id).toBe('id-value');
    expect(checksum.checksum).toBe('checksum-value');
  });
});

describe('reference.util - getJobChecksum', () => {
  describe('basic functionality', () => {
    it('should return undefined when reference is null', () => {
      const checksum = ReferenceUtil.getJobChecksum(null);
      expect(checksum).toBeUndefined();
    });

    it('should return undefined when reference is undefined', () => {
      const checksum = ReferenceUtil.getJobChecksum(undefined);
      expect(checksum).toBeUndefined();
    });

    it('should return undefined when compositeID is missing', () => {
      const reference = {
        [ReferenceUtil.CHECKSUM]: 'test-checksum',
        moonwalk: 'test-emmyscore-id',
      };
      const checksum = ReferenceUtil.getJobChecksum(reference);
      expect(checksum).toBeUndefined();
    });

    it('should return undefined when compositeID is empty string', () => {
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: '',
        [ReferenceUtil.CHECKSUM]: 'test-checksum',
        moonwalk: 'test-emmyscore-id',
      };
      const checksum = ReferenceUtil.getJobChecksum(reference);
      expect(checksum).toBeUndefined();
    });
  });

  describe('successful checksum extraction', () => {
    it('should create job checksum from reference with all fields', () => {
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: 'job#source-value:id-value',
        [ReferenceUtil.CHECKSUM]: 'checksum-value',
        moonwalk: 'emmyscore-id-value',
      };

      const checksum = ReferenceUtil.getJobChecksum(reference);

      expect(checksum).toBeDefined();
      expect(checksum.source).toBe('source-value');
      expect(checksum.id).toBe('id-value');
      expect(checksum.id2).toBeUndefined();
      expect(checksum.checksum).toBe('checksum-value');
      expect(checksum.emmyScoreID).toBe('emmyscore-id-value');
    });

    it('should create job checksum with id2 when present in composite ID', () => {
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: 'job#source-value:id-value:id2-value',
        [ReferenceUtil.CHECKSUM]: 'checksum-value',
        moonwalk: 'emmyscore-id-value',
      };

      const checksum = ReferenceUtil.getJobChecksum(reference);

      expect(checksum).toBeDefined();
      expect(checksum.source).toBe('source-value');
      expect(checksum.id).toBe('id-value');
      expect(checksum.id2).toBe('id2-value');
      expect(checksum.checksum).toBe('checksum-value');
      expect(checksum.emmyScoreID).toBe('emmyscore-id-value');
    });

    it('should handle missing checksum by defaulting to empty string', () => {
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: 'job#source-value:id-value',
        moonwalk: 'emmyscore-id-value',
      };

      const checksum = ReferenceUtil.getJobChecksum(reference);

      expect(checksum).toBeDefined();
      expect(checksum.source).toBe('source-value');
      expect(checksum.id).toBe('id-value');
      expect(checksum.checksum).toBe('');
      expect(checksum.emmyScoreID).toBe('emmyscore-id-value');
    });

    it('should handle missing emmyScoreID', () => {
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: 'job#source-value:id-value',
        [ReferenceUtil.CHECKSUM]: 'checksum-value',
      };

      const checksum = ReferenceUtil.getJobChecksum(reference);

      expect(checksum).toBeDefined();
      expect(checksum.source).toBe('source-value');
      expect(checksum.id).toBe('id-value');
      expect(checksum.checksum).toBe('checksum-value');
      expect(checksum.emmyScoreID).toBeUndefined();
    });
  });

  describe('custom checksum location', () => {
    it('should use custom checksum location when provided', () => {
      const customChecksumKey = 'custom_checksum_location';
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: 'job#source-value:id-value',
        [ReferenceUtil.CHECKSUM]: 'default-checksum',
        [customChecksumKey]: 'custom-checksum-value',
        moonwalk: 'emmyscore-id-value',
      };

      const checksum = ReferenceUtil.getJobChecksum(reference, customChecksumKey);

      expect(checksum).toBeDefined();
      expect(checksum.source).toBe('source-value');
      expect(checksum.id).toBe('id-value');
      expect(checksum.checksum).toBe('custom-checksum-value');
      expect(checksum.emmyScoreID).toBe('emmyscore-id-value');
    });

    it('should default to empty string when custom checksum location is missing', () => {
      const customChecksumKey = 'non_existent_checksum';
      const reference = {
        [ReferenceUtil.COMPOSITE_ID]: 'job#source-value:id-value',
        [ReferenceUtil.CHECKSUM]: 'default-checksum',
        moonwalk: 'emmyscore-id-value',
      };

      const checksum = ReferenceUtil.getJobChecksum(reference, customChecksumKey);

      expect(checksum).toBeDefined();
      expect(checksum.source).toBe('source-value');
      expect(checksum.id).toBe('id-value');
      expect(checksum.checksum).toBe('');
      expect(checksum.emmyScoreID).toBe('emmyscore-id-value');
    });
  });
});
